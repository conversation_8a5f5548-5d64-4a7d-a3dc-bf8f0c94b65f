#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理模块
"""

import os
import hashlib
import uuid
import shutil
import subprocess
import tempfile
import logging
from datetime import datetime
from typing import Optional, Dict, Any, Tuple
from pathlib import Path

try:
    from docx import Document
    from docx.opc.exceptions import PackageNotFoundError
except ImportError:
    Document = None
    PackageNotFoundError = Exception

try:
    from openpyxl import load_workbook
    from openpyxl.utils.exceptions import InvalidFileException
except ImportError:
    load_workbook = None
    InvalidFileException = Exception

try:
    from pptx import Presentation
    from pptx.exc import PackageNotFoundError as PptxPackageNotFoundError
except ImportError:
    Presentation = None
    PptxPackageNotFoundError = Exception

try:
    import olefile
    import struct
    import chardet
except ImportError:
    olefile = None
    struct = None
    chardet = None


class DocumentProcessor:
    """文档处理器"""

    # 支持的文件扩展名
    SUPPORTED_EXTENSIONS = {'.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.wps', '.et'}

    def __init__(self, output_dir: str = "/data/save_doc"):
        """
        初始化文档处理器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def calculate_file_hash(self, file_path: str) -> str:
        """
        计算文件哈希值

        Args:
            file_path: 文件路径

        Returns:
            文件的SHA256哈希值
        """
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logging.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""

    def get_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件元数据

        Args:
            file_path: 文件路径

        Returns:
            文件元数据字典
        """
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_ctime)
            }
        except Exception as e:
            logging.error(f"获取文件元数据失败 {file_path}: {e}")
            return {}

    def extract_docx_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[datetime]]:
        """
        提取DOCX文档内容和元数据

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not Document:
            return "", None, None

        try:
            doc = Document(file_path)

            # 提取文本内容
            content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())

            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content.append(" | ".join(row_text))

            text_content = "\n".join(content)

            # 提取元数据
            creator = None
            created_time = None

            try:
                core_props = doc.core_properties
                creator = core_props.author
                created_time = core_props.created
            except:
                pass

            return text_content, creator, created_time

        except (PackageNotFoundError, Exception) as e:
            logging.warning(f"使用python-docx提取DOCX内容失败 {file_path}: {e}")
            return "", None, None

    def extract_xlsx_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[datetime]]:
        """
        提取XLSX文档内容和元数据

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not load_workbook:
            return "", None, None

        try:
            workbook = load_workbook(file_path, data_only=True)
            content = []

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                content.append(f"工作表: {sheet_name}")

                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else "" for cell in row]
                    if any(cell.strip() for cell in row_data):
                        content.append(" | ".join(row_data))

            text_content = "\n".join(content)

            # 提取元数据
            creator = None
            created_time = None

            try:
                props = workbook.properties
                creator = props.creator
                created_time = props.created
            except:
                pass

            return text_content, creator, created_time

        except (InvalidFileException, Exception) as e:
            logging.warning(f"使用openpyxl提取XLSX内容失败 {file_path}: {e}")
            return "", None, None

    def extract_pptx_content(self, file_path: str) -> Tuple[str, Optional[str], Optional[datetime]]:
        """
        提取PPTX文档内容和元数据

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not Presentation:
            return "", None, None

        try:
            prs = Presentation(file_path)
            content = []

            for i, slide in enumerate(prs.slides, 1):
                content.append(f"幻灯片 {i}:")

                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        content.append(shape.text.strip())

            text_content = "\n".join(content)

            # 提取元数据
            creator = None
            created_time = None

            try:
                core_props = prs.core_properties
                creator = core_props.author
                created_time = core_props.created
            except:
                pass

            return text_content, creator, created_time

        except (PptxPackageNotFoundError, Exception) as e:
            logging.warning(f"使用python-pptx提取PPTX内容失败 {file_path}: {e}")
            return "", None, None

    def extract_with_libreoffice(self, file_path: str) -> str:
        """
        使用LibreOffice提取文档内容

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # 转换为文本文件
                cmd = [
                    'libreoffice',
                    '--headless',
                    '--convert-to', 'txt',
                    '--outdir', temp_dir,
                    file_path
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    env=dict(os.environ, HOME=temp_dir)
                )

                if result.returncode == 0:
                    # 查找生成的文本文件
                    txt_files = list(Path(temp_dir).glob("*.txt"))
                    if txt_files:
                        with open(txt_files[0], 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        return content.strip()
                else:
                    logging.warning(f"LibreOffice转换失败: {result.stderr}")

        except subprocess.TimeoutExpired:
            logging.warning(f"LibreOffice转换超时: {file_path}")
        except Exception as e:
            logging.warning(f"LibreOffice转换异常 {file_path}: {e}")

        return ""

    def extract_doc_content_with_olefile(self, file_path: str) -> Tuple[str, Optional[str], Optional[datetime]]:
        """
        使用olefile提取DOC文档内容

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not olefile:
            return "", None, None

        try:
            if not olefile.isOleFile(file_path):
                return "", None, None

            ole = olefile.OleFileIO(file_path)

            # 提取文档内容
            content = ""
            creator = None
            create_time = None

            # 列出所有流
            streams = ole.listdir()
            logging.debug(f"OLE文件中的流: {streams}")

            # 尝试读取WordDocument流
            if 'WordDocument' in streams:
                try:
                    with ole.open('WordDocument') as word_stream:
                        data = word_stream.read()

                        # 简单的文本提取
                        text_parts = []

                        # 尝试不同的编码方式
                        for encoding in ['utf-16le', 'utf-8', 'gbk', 'cp1252']:
                            try:
                                decoded_text = data.decode(encoding, errors='ignore')
                                # 提取可打印字符
                                printable_chars = ''.join(char for char in decoded_text
                                                         if char.isprintable() and ord(char) > 31)
                                if len(printable_chars) > len(text_parts):
                                    text_parts = list(printable_chars)
                                    break
                            except:
                                continue

                        # 如果解码失败，尝试字节级提取
                        if not text_parts:
                            for i in range(len(data)):
                                try:
                                    if 32 <= data[i] <= 126:  # ASCII可打印字符
                                        text_parts.append(chr(data[i]))
                                    elif data[i] == 0:  # 跳过空字节
                                        continue
                                except:
                                    continue

                        content = ''.join(text_parts)
                        # 清理内容
                        content = ' '.join(content.split())

                except Exception as e:
                    logging.warning(f"读取WordDocument流失败: {e}")

            # 尝试提取文档属性
            try:
                if '\x05SummaryInformation' in ole.listdir():
                    summary_stream = ole._olestream('\x05SummaryInformation')
                    # 这里可以解析文档属性，但比较复杂
                    # 暂时跳过详细实现
                    pass
            except Exception as e:
                logging.warning(f"读取文档属性失败: {e}")

            ole.close()
            return content, creator, create_time

        except Exception as e:
            logging.warning(f"使用olefile提取DOC内容失败 {file_path}: {e}")
            return "", None, None

    def extract_xls_content_with_olefile(self, file_path: str) -> Tuple[str, Optional[str], Optional[datetime]]:
        """
        使用olefile提取XLS文档内容

        Args:
            file_path: 文件路径

        Returns:
            (文档内容, 创建者, 创建时间)
        """
        if not olefile:
            return "", None, None

        try:
            if not olefile.isOleFile(file_path):
                return "", None, None

            ole = olefile.OleFileIO(file_path)
            content_parts = []
            creator = None
            create_time = None

            # 列出所有流
            streams = ole.listdir()
            logging.debug(f"XLS文件中的流: {streams}")

            # 尝试读取Workbook流
            if 'Workbook' in streams:
                try:
                    with ole.open('Workbook') as workbook_stream:
                        data = workbook_stream.read()

                        # 简单的文本提取
                        text_parts = []

                        # 尝试不同的编码方式
                        for encoding in ['utf-8', 'gbk', 'cp1252', 'utf-16le']:
                            try:
                                decoded_text = data.decode(encoding, errors='ignore')
                                # 提取可打印字符
                                printable_chars = ''.join(char for char in decoded_text
                                                         if char.isprintable() and ord(char) > 31)
                                if len(printable_chars) > len(text_parts):
                                    text_parts = list(printable_chars)
                                    if len(printable_chars) > 50:  # 如果找到足够的文本就停止
                                        break
                            except:
                                continue

                        # 如果解码失败，尝试字节级提取
                        if not text_parts:
                            for i in range(len(data)):
                                try:
                                    if 32 <= data[i] <= 126:  # ASCII可打印字符
                                        text_parts.append(chr(data[i]))
                                except:
                                    continue

                        content = ''.join(text_parts)
                        content_parts.append(content)

                except Exception as e:
                    logging.warning(f"读取Workbook流失败: {e}")

            ole.close()

            # 清理和组合内容
            final_content = ' '.join(content_parts)
            final_content = ' '.join(final_content.split())

            return final_content, creator, create_time

        except Exception as e:
            logging.warning(f"使用olefile提取XLS内容失败 {file_path}: {e}")
            return "", None, None

    def extract_with_antiword(self, file_path: str) -> str:
        """
        使用antiword提取DOC文档内容

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            result = subprocess.run(
                ['antiword', file_path],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                return result.stdout.strip()
            else:
                logging.warning(f"antiword转换失败: {result.stderr}")

        except subprocess.TimeoutExpired:
            logging.warning(f"antiword转换超时: {file_path}")
        except FileNotFoundError:
            logging.debug("antiword未安装，跳过")
        except Exception as e:
            logging.warning(f"antiword转换异常 {file_path}: {e}")

        return ""

    def extract_with_catdoc(self, file_path: str) -> str:
        """
        使用catdoc提取DOC文档内容

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            result = subprocess.run(
                ['catdoc', file_path],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                return result.stdout.strip()
            else:
                logging.warning(f"catdoc转换失败: {result.stderr}")

        except subprocess.TimeoutExpired:
            logging.warning(f"catdoc转换超时: {file_path}")
        except FileNotFoundError:
            logging.debug("catdoc未安装，跳过")
        except Exception as e:
            logging.warning(f"catdoc转换异常 {file_path}: {e}")

        return ""

    def extract_xls_with_pandas(self, file_path: str) -> str:
        """
        使用pandas提取XLS文档内容（如果可用）

        Args:
            file_path: 文件路径

        Returns:
            文档内容
        """
        try:
            import pandas as pd

            # 读取所有工作表 - 尝试不同的引擎
            engines_to_try = ['openpyxl', 'xlrd', None]

            for engine in engines_to_try:
                try:
                    if engine:
                        excel_file = pd.ExcelFile(file_path, engine=engine)
                    else:
                        excel_file = pd.ExcelFile(file_path)

                    content_parts = []

                    for sheet_name in excel_file.sheet_names:
                        content_parts.append(f"工作表: {sheet_name}")

                        try:
                            if engine:
                                df = pd.read_excel(file_path, sheet_name=sheet_name, engine=engine)
                            else:
                                df = pd.read_excel(file_path, sheet_name=sheet_name)

                            # 转换为文本
                            for index, row in df.iterrows():
                                row_text = []
                                for col in df.columns:
                                    cell_value = row[col]
                                    if pd.notna(cell_value):
                                        row_text.append(str(cell_value))
                                if row_text:
                                    content_parts.append(" | ".join(row_text))

                        except Exception as e:
                            logging.warning(f"读取工作表 {sheet_name} 失败: {e}")

                    if content_parts:
                        return "\n".join(content_parts)

                except Exception as e:
                    logging.debug(f"使用引擎 {engine} 读取Excel文件失败: {e}")
                    continue

            logging.warning(f"所有引擎都无法读取Excel文件: {file_path}")
            return ""

        except ImportError:
            logging.debug("pandas未安装，跳过")
        except Exception as e:
            logging.warning(f"pandas提取XLS内容失败 {file_path}: {e}")

        return ""

    def extract_document_content(self, file_path: str) -> Dict[str, Any]:
        """
        提取文档内容和元数据

        Args:
            file_path: 文件路径

        Returns:
            包含文档信息的字典
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()

        # 初始化结果
        result = {
            'content': '',
            'creator': None,
            'create_time': None,
            'original_filename': file_path.name,
            'file_hash': self.calculate_file_hash(str(file_path))
        }

        # 获取文件系统元数据作为备选
        file_metadata = self.get_file_metadata(str(file_path))
        fallback_time = file_metadata.get('modified_time')

        # 尝试使用专用库提取内容
        content = ""
        creator = None
        create_time = None

        if extension in {'.docx'}:
            content, creator, create_time = self.extract_docx_content(str(file_path))
        elif extension in {'.xlsx'}:
            content, creator, create_time = self.extract_xlsx_content(str(file_path))
        elif extension in {'.pptx'}:
            content, creator, create_time = self.extract_pptx_content(str(file_path))
        elif extension in {'.doc'}:
            # 对于DOC文件，尝试多种方法
            logging.info(f"处理DOC文件，尝试多种提取方法: {file_path}")

            # 方法1: 尝试olefile
            content, creator, create_time = self.extract_doc_content_with_olefile(str(file_path))

            # 方法2: 如果olefile失败，尝试antiword
            if not content:
                logging.info(f"olefile提取失败，尝试antiword: {file_path}")
                content = self.extract_with_antiword(str(file_path))

            # 方法3: 如果antiword失败，尝试catdoc
            if not content:
                logging.info(f"antiword提取失败，尝试catdoc: {file_path}")
                content = self.extract_with_catdoc(str(file_path))

        elif extension in {'.xls'}:
            # 对于XLS文件，尝试多种方法
            logging.info(f"处理XLS文件，尝试多种提取方法: {file_path}")

            # 方法1: 尝试olefile
            content, creator, create_time = self.extract_xls_content_with_olefile(str(file_path))

            # 方法2: 如果olefile失败，尝试pandas
            if not content:
                logging.info(f"olefile提取失败，尝试pandas: {file_path}")
                content = self.extract_xls_with_pandas(str(file_path))

        elif extension in {'.ppt'}:
            # 对于PPT文件，主要依赖LibreOffice
            logging.info(f"处理PPT文件: {file_path}")

        elif extension in {'.wps', '.et'}:
            # 对于WPS文件，主要依赖LibreOffice
            logging.info(f"处理WPS文件: {file_path}")

        # 如果所有专用方法都失败，尝试LibreOffice作为最后手段
        if not content:
            logging.info(f"专用方法提取失败，尝试使用LibreOffice: {file_path}")
            content = self.extract_with_libreoffice(str(file_path))

        # 更新结果
        result['content'] = content
        result['creator'] = creator
        result['create_time'] = create_time or fallback_time

        return result

    def save_document_with_uuid(self, file_path: str) -> Optional[str]:
        """
        将文档保存到输出目录，使用UUID作为文件名

        Args:
            file_path: 原始文件路径

        Returns:
            新文件名（包含扩展名）或None（如果失败）
        """
        try:
            file_path = Path(file_path)
            extension = file_path.suffix

            # 生成UUID文件名
            new_filename = str(uuid.uuid4()).replace('-', '') + extension
            new_file_path = self.output_dir / new_filename

            # 复制文件
            shutil.copy2(file_path, new_file_path)
            logging.info(f"文件已保存: {file_path.name} -> {new_filename}")

            return new_filename

        except Exception as e:
            logging.error(f"保存文件失败 {file_path}: {e}")
            return None

    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的格式

        Args:
            file_path: 文件路径

        Returns:
            是否支持
        """
        extension = Path(file_path).suffix.lower()
        return extension in self.SUPPORTED_EXTENSIONS

    def get_files_in_directory(self, directory: str) -> list:
        """
        获取目录中所有支持的文档文件

        Args:
            directory: 目录路径

        Returns:
            文件路径列表
        """
        files = []
        directory = Path(directory)

        if not directory.exists():
            logging.error(f"目录不存在: {directory}")
            return files

        for file_path in directory.rglob("*"):
            if file_path.is_file() and self.is_supported_file(str(file_path)):
                files.append(str(file_path))

        return sorted(files)
