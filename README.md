# 文档保存系统

一个功能完整的文档保存系统，支持多种Office文档格式的内容提取和数据库存储。

## 功能特性

- **多格式支持**: 支持 doc、docx、xls、xlsx、ppt、pptx、wps、et 等格式
- **智能内容提取**: 使用专用库和LibreOffice多重提取策略
- **数据库存储**: 自动连接SQL Server数据库并创建表结构
- **重复检测**: 基于文件哈希值避免重复存储
- **元数据提取**: 提取文档创建者、创建时间等元信息
- **进度显示**: 实时显示处理进度和统计信息
- **健壮性**: 多种异常处理和错误恢复机制

## 系统要求

- Ubuntu 22.04 Server
- Python 3.10+
- LibreOffice 7.3+
- Microsoft ODBC Driver 18 for SQL Server
- SQL Server 数据库

## 安装步骤

1. **克隆或下载项目文件**
   ```bash
   # 确保所有文件都在同一目录下
   ls -la
   # 应该看到: document_manager.py, database_handler.py, document_processor.py, 
   #          config_loader.py, nbfwq.json, requirements.txt, install_dependencies.sh
   ```

2. **运行安装脚本**
   ```bash
   ./install_dependencies.sh
   ```

3. **配置数据库连接**
   
   编辑 `nbfwq.json` 文件，修改数据库连接信息：
   ```json
   {
       "driver": "{ODBC Driver 18 for SQL Server}",
       "server": "your_server_ip",
       "database": "DocumentDB",
       "encrypt": "yes",
       "trustServerCertificate": "yes",
       "uid": "your_username",
       "pwd": "your_password"
   }
   ```

## 使用方法

### 基本用法

```bash
python3 document_manager.py <输入目录>
```

### 指定输出目录

```bash
python3 document_manager.py <输入目录> -o <输出目录>
```

### 示例

```bash
# 处理当前目录下的documents文件夹中的所有文档
python3 document_manager.py ./documents

# 处理指定目录并保存到自定义位置
python3 document_manager.py /home/<USER>/docs -o /custom/save/path

# 查看帮助信息
python3 document_manager.py -h
```

## 数据库表结构

系统会自动创建 `documents` 表，包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| document_hash | NVARCHAR(64) | 文档哈希值（唯一） |
| original_filename | NVARCHAR(255) | 原始文件名 |
| saved_filename | NVARCHAR(255) | 保存后的UUID文件名 |
| document_content | NTEXT | 文档正文内容 |
| document_creator | NVARCHAR(100) | 文档创建者 |
| document_create_time | DATETIME | 文档创建时间 |
| upload_time | DATETIME | 上传时间 |
| uploader | NVARCHAR(100) | 上传者（默认：Zhang Pengfei） |
| document_keywords | NVARCHAR(500) | 文档关键字（预留） |
| document_title | NVARCHAR(255) | 文档标题（预留） |
| document_summary | NTEXT | 文档摘要（预留） |
| document_source | NVARCHAR(255) | 文档来源（预留） |
| document_type | NVARCHAR(50) | 文档类型（预留） |
| department | NVARCHAR(100) | 所属科室（预留） |
| work_specialty | NVARCHAR(100) | 工作专业（预留） |
| analysis_time | DATETIME | 分析时间（默认：1999-01-01） |

## 工作流程

1. **系统初始化**
   - 检查输入输出目录
   - 连接数据库并测试
   - 创建文档表（如不存在）

2. **文件扫描**
   - 递归扫描输入目录
   - 筛选支持的文档格式

3. **文档处理**
   - 计算文件哈希值
   - 检查是否已存在（避免重复）
   - 提取文档内容和元数据
   - 生成UUID文件名并保存
   - 插入数据库记录

4. **进度显示**
   - 实时显示处理进度
   - 统计成功、失败、重复文件数量

## 日志文件

系统运行时会生成 `document_manager.log` 日志文件，记录详细的处理信息和错误。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `nbfwq.json` 配置
   - 确认SQL Server服务运行正常
   - 检查网络连接和防火墙设置

2. **LibreOffice转换失败**
   - 确认LibreOffice已正确安装
   - 检查文件权限
   - 查看日志文件获取详细错误信息

3. **权限问题**
   - 确保对输入目录有读权限
   - 确保对输出目录有写权限
   - 检查 `/data/save_doc` 目录权限

### 调试模式

查看详细日志：
```bash
tail -f document_manager.log
```

## 注意事项

- 文档内容使用UTF-16编码存储到数据库
- 系统会自动跳过已存在的文档（基于哈希值）
- 大文件处理可能需要较长时间
- 建议定期备份数据库和保存的文档文件
